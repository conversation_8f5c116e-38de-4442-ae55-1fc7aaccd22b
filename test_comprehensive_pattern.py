#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试综合格局分析的提示词生成
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/home/<USER>/riyue-llm/myproject/demo_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

def test_comprehensive_pattern_prompt():
    """测试综合格局分析提示词生成"""
    try:
        from api.prompts.prompt_engine import prompt_engine
        
        print("🔧 测试综合格局分析提示词生成...")
        print("=" * 60)
        
        # 测试参数
        analysis_direction = "comprehensive_pattern"
        domain = "overall_pattern"
        time_scope = "lifetime"
        user_question = "请进行综合格局分析"
        
        print(f"📋 测试参数:")
        print(f"  - analysis_direction: {analysis_direction}")
        print(f"  - domain: {domain}")
        print(f"  - time_scope: {time_scope}")
        print(f"  - user_question: {user_question}")
        print("-" * 40)
        
        # 生成提示词
        system_prompt, user_prompt = prompt_engine.generate_prompt(
            analysis_direction, domain, time_scope, user_question
        )
        
        print("🤖 生成的系统提示词:")
        print(system_prompt)
        print("\n" + "=" * 60)
        
        print("👤 生成的用户提示词模板:")
        print(user_prompt)
        print("\n" + "=" * 60)
        
        print("✅ 提示词生成成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_enum_validation():
    """测试枚举值验证"""
    try:
        from api.prompts.categories import AnalysisDirection, Domain, TimeScope
        
        print("\n🔍 测试枚举值验证...")
        print("=" * 60)
        
        # 测试新增的枚举值
        test_values = [
            ("AnalysisDirection.COMPREHENSIVE_PATTERN", AnalysisDirection.COMPREHENSIVE_PATTERN),
            ("Domain.OVERALL_PATTERN", Domain.OVERALL_PATTERN),
            ("TimeScope.LIFETIME", TimeScope.LIFETIME)
        ]
        
        for name, enum_value in test_values:
            print(f"✅ {name}: {enum_value.value}")
        
        print("\n✅ 所有枚举值验证通过！")
        
    except Exception as e:
        print(f"❌ 枚举验证失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enum_validation()
    test_comprehensive_pattern_prompt()
