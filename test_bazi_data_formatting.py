#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试八字数据格式化
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/home/<USER>/riyue-llm/myproject/demo_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo_api.settings')
django.setup()

def test_data_formatting():
    """测试数据格式化"""
    try:
        from api.consumers.bazi_advice_consumer import BaziAdviceConsumer
        
        print("🔧 测试八字数据格式化...")
        print("=" * 60)
        
        # 模拟前端发送的数据结构
        test_data = {
            "type": "bazi_analysis",
            "data": {
                "eight_char": "己卯 壬申 丙午 癸巳",
                "gender": "男",
                "shishen": {
                    "gan_shishen": {
                        "year": "伤官",
                        "month": "七杀",
                        "day": "日主",
                        "time": "正官"
                    },
                    "zhi_shishen": {
                        "year": ["正印"],
                        "month": ["偏财", "七杀", "食神"],
                        "day": ["劫财", "伤官"],
                        "time": ["比肩", "偏财", "食神"]
                    }
                },
                "shensha": {
                    "pillars": {
                        "年柱": {
                            "ganzhi": "己卯",
                            "shenshas": ["太极", "空亡", "桃花"],
                            "count": 3
                        },
                        "月柱": {
                            "ganzhi": "壬申",
                            "shenshas": ["天乙", "月德", "文昌", "学堂", "驿马", "金舆", "劫煞", "元辰", "空亡"],
                            "count": 9
                        },
                        "日柱": {
                            "ganzhi": "丙午",
                            "shenshas": ["天喜", "羊刃", "孤鸾", "阴差阳错"],
                            "count": 4
                        },
                        "时柱": {
                            "ganzhi": "癸巳",
                            "shenshas": ["天德", "驿马", "禄神", "童子", "天厨", "孤辰", "亡神", "丧门"],
                            "count": 8
                        }
                    },
                    "total_count": 24,
                    "unique_count": 22,
                    "unique_shenshas": ["丧门", "亡神", "元辰", "劫煞", "天乙", "天厨", "天喜", "天德", "太极", "孤辰", "孤鸾", "学堂", "文昌", "月德", "桃花", "禄神", "空亡", "童子", "羊刃", "金舆", "阴差阳错", "驿马"]
                },
                "analysis_direction": "comprehensive_pattern",
                "domain": "overall_pattern",
                "time_scope": "lifetime",
                "user_question": "请进行综合格局分析",
                "detailed_info": {
                    "birth_info": {
                        "solar_date": "1999-08-22 09:00:00 星期日 (邓小平诞辰纪念日) 狮子座",
                        "lunar_date": "一九九九年七月十二 己卯(兔)年 壬申(猴)月 丙午(马)日 巳(蛇)时",
                        "birth_details": {
                            "year": 1999,
                            "month": 8,
                            "day": 22,
                            "hour": 9,
                            "minute": 0
                        }
                    },
                    "bazi_pillars": {
                        "year_pillar": "己卯",
                        "month_pillar": "壬申",
                        "day_pillar": "丙午",
                        "time_pillar": "癸巳"
                    },
                    "current_dayun": {
                        "ganzhi": "己巳",
                        "start_age": 26,
                        "end_age": 35
                    },
                    "liu_nian_info": {
                        "liuNian": {"ganzhi": "乙巳"},
                        "liuYue": {"ganzhi": "甲申"},
                        "liuRi": {"ganzhi": "庚子"}
                    }
                }
            }
        }
        
        print("📨 模拟前端数据:")
        print(json.dumps(test_data, ensure_ascii=False, indent=2))
        print("\n" + "=" * 60)
        
        # 创建消费者实例并测试数据处理
        consumer = BaziAdviceConsumer()
        
        # 测试参数化请求检测
        is_parameterized = consumer._is_parameterized_request(test_data)
        print(f"🔍 是否为参数化请求: {is_parameterized}")
        
        if is_parameterized:
            # 测试参数化请求处理
            system_prompt, user_message = consumer._handle_parameterized_request(test_data)
            
            print("\n🤖 生成的系统提示词:")
            print(system_prompt[:500] + "..." if len(system_prompt) > 500 else system_prompt)
            
            print("\n👤 生成的用户消息:")
            print(user_message)
        else:
            print("❌ 未识别为参数化请求")
            
        print("\n" + "=" * 60)
        print("✅ 数据格式化测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_formatting()
